# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/7
File Name: track.py
Description: 轨道添加功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pyJianYingDraft as draft
from .config import DRAFT_FOLDER
import uuid
import json
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Optional


@dataclass
class TrackAddReqDto:
    """轨道添加请求参数"""
    draft_id: str
    track_type: str
    track_name: Optional[str] = None
    mute: bool = False
    relative_index: int = 0
    absolute_index: Optional[int] = None
    
    # 轨道类型常量
    TRACK_TYPE_VIDEO = "video"
    TRACK_TYPE_AUDIO = "audio"
    TRACK_TYPE_TEXT = "text"


@dataclass
class TrackEntity:
    """轨道实体"""
    id: str
    draft_id: str
    track_type: str
    track_name: str = ""
    mute: bool = False
    relative_index: int = 0
    absolute_index: Optional[int] = None
    create_time: str = ""
    update_time: str = ""
    
    def __post_init__(self):
        if not self.track_name:
            self.track_name = self.id
        if not self.create_time:
            self.create_time = datetime.now().isoformat()
        if not self.update_time:
            self.update_time = datetime.now().isoformat()


class TrackService:
    """轨道服务类"""
    
    def __init__(self, base_draft_path: str):
        self.base_draft_path = base_draft_path
        # 确保基础路径存在
        os.makedirs(base_draft_path, exist_ok=True)
    
    def add_track(self, req_dto: TrackAddReqDto) -> str:
        """添加轨道"""
        print(f"开始添加轨道，draftId={req_dto.draft_id}, trackType={req_dto.track_type}, trackName={req_dto.track_name}")
        
        # 检查草稿是否存在
        if not self._draft_exists(req_dto.draft_id):
            raise ValueError(f"草稿不存在: {req_dto.draft_id}")
        
        # 校验同类轨道名称唯一性
        if req_dto.track_name:
            if self._track_name_exists(req_dto.draft_id, req_dto.track_type, req_dto.track_name):
                raise ValueError(f"同类轨道中已存在名称为 '{req_dto.track_name}' 的轨道，请更换名称")
        
        # 生成轨道ID
        track_id = str(uuid.uuid4()).replace('-', '').upper()[:24]  # 模拟ObjectId格式
        
        # 创建轨道实体
        track_entity = TrackEntity(
            id=track_id,
            draft_id=req_dto.draft_id,
            track_type=req_dto.track_type,
            track_name=req_dto.track_name or track_id,
            mute=req_dto.mute,
            relative_index=req_dto.relative_index,
            absolute_index=req_dto.absolute_index
        )
        
        # 保存轨道到文件系统
        self._save_track_to_file(track_entity)
        
        # 使用pyJianYingDraft添加轨道
        self._add_jianying_track(track_entity)
        
        print(f"轨道添加成功，id={track_id}")
        return track_id
    
    def _draft_exists(self, draft_id: str) -> bool:
        """检查草稿是否存在"""
        draft_folder_path = os.path.join(self.base_draft_path, draft_id)
        metadata_file = os.path.join(draft_folder_path, "draft_metadata.json")
        return os.path.exists(metadata_file)
    
    def _track_name_exists(self, draft_id: str, track_type: str, track_name: str) -> bool:
        """检查同类轨道名称是否已存在"""
        draft_folder = os.path.join(self.base_draft_path, draft_id)
        if not os.path.exists(draft_folder):
            return False

        # 检查草稿文件夹中的所有track_*.json文件
        for file_name in os.listdir(draft_folder):
            if file_name.startswith('track_') and file_name.endswith('.json'):
                track_path = os.path.join(draft_folder, file_name)
                try:
                    with open(track_path, 'r', encoding='utf-8') as f:
                        track_data = json.load(f)
                        if (track_data.get('track_type') == track_type and
                            track_data.get('track_name') == track_name):
                            return True
                except Exception:
                    continue
        return False
    
    def _save_track_to_file(self, track_entity: TrackEntity):
        """将轨道实体保存到文件"""
        # 直接保存到草稿文件夹中，不创建额外的tracks子文件夹
        draft_folder = os.path.join(self.base_draft_path, track_entity.draft_id)

        # 保存轨道数据到草稿文件夹中
        track_file = os.path.join(draft_folder, f"track_{track_entity.id}.json")
        with open(track_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(track_entity), f, ensure_ascii=False, indent=2)
    
    def _add_jianying_track(self, track_entity: TrackEntity):
        """使用pyJianYingDraft添加轨道"""
        try:
            # 读取草稿信息
            draft_metadata_file = os.path.join(self.base_draft_path, track_entity.draft_id, "draft_metadata.json")
            with open(draft_metadata_file, 'r', encoding='utf-8') as f:
                draft_data = json.load(f)

            draft_path = draft_data.get('draft_path', DRAFT_FOLDER)
            draft_name = draft_data.get('draft_name', track_entity.draft_id)

            draft_folder = draft.DraftFolder(draft_path)
            script = draft_folder.load_template(draft_name)

            # 根据类型添加轨道
            track_params = {}

            # 只有在指定了轨道名称时才传递
            if track_entity.track_name and track_entity.track_name != track_entity.id:
                track_params["track_name"] = track_entity.track_name

            # 添加其他参数
            track_params["mute"] = track_entity.mute
            track_params["relative_index"] = track_entity.relative_index

            if track_entity.absolute_index is not None:
                track_params["absolute_index"] = track_entity.absolute_index
                # 移除relative_index，因为不能同时使用
                del track_params["relative_index"]

            if track_entity.track_type == TrackAddReqDto.TRACK_TYPE_VIDEO:
                script.add_track(draft.TrackType.video, **track_params)
            elif track_entity.track_type == TrackAddReqDto.TRACK_TYPE_AUDIO:
                script.add_track(draft.TrackType.audio, **track_params)
            elif track_entity.track_type == TrackAddReqDto.TRACK_TYPE_TEXT:
                script.add_track(draft.TrackType.text, **track_params)
            else:
                raise ValueError(f"不支持的轨道类型: {track_entity.track_type}")

            script.save()
            print(f"成功添加轨道到剪映草稿: {track_entity.track_name}")

        except Exception as e:
            print(f"添加剪映轨道失败: {e}")
            # 如果是无效轨道类型，抛出异常
            if "不支持的轨道类型" in str(e):
                raise e
            # 其他错误，保存基本信息但不抛出异常


def add_track(draft_id, track_type, track_name=None, mute=False, relative_index=0, absolute_index=None, draft_path=""):
    """添加轨道的便捷函数"""
    # 使用指定的草稿路径或默认路径
    target_path = draft_path if draft_path else DRAFT_FOLDER
    
    req_dto = TrackAddReqDto(
        draft_id=draft_id,
        track_type=track_type,
        track_name=track_name,
        mute=mute,
        relative_index=relative_index,
        absolute_index=absolute_index
    )
    
    # 创建服务实例
    service = TrackService(target_path)
    
    # 添加轨道
    track_id = service.add_track(req_dto)
    
    return {
        "message": "添加轨道成功",
        "track_id": track_id,
        "draft_id": draft_id,
        "track_type": track_type,
        "track_name": track_name or track_id
    }


# 测试代码
if __name__ == "__main__":
    # 测试添加轨道
    print("测试轨道添加功能...")
    
    # 这里需要一个已存在的草稿ID进行测试
    # result = add_track("test_draft_id", "video", "测试视频轨道")
    # print(result)
