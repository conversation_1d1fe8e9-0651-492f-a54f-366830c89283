package com.esther.jianyingdraft.service.impl

import com.esther.jianyingdraft.domain.req.TrackAddReqDto
import com.esther.jianyingdraft.entity.TrackEntity
import com.esther.jianyingdraft.exception.SysException
import com.esther.jianyingdraft.service.TrackService
import com.esther.jianyingdraft.utils.DraftUtils
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import kotlinx.coroutines.reactive.awaitSingle
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.*

/**
 * 轨道服务实现类，实现轨道相关操作。
 * <AUTHOR>
 */
@Service
class TrackServiceImpl @Autowired constructor(
    private val reactiveMongoTemplate: ReactiveMongoTemplate
) : TrackService {
    private val logger = LoggerFactory.getLogger(TrackServiceImpl::class.java)

    /**
     * 添加轨道到MongoDB
     * @param req 轨道添加请求参数
     * @return 新增轨道的id
     */
    override suspend fun addTrack(req: TrackAddReqDto): String {
        logger.info("开始添加轨道，draftId={}, trackType={}, trackName={}", req.draftId, req.trackType, req.trackName)
        DraftUtils.checkApiKeyByDraftId(req.draftId, reactiveMongoTemplate)
        // 校验同类轨道名称唯一性
        if (!req.trackName.isNullOrBlank()) {
            val query = Query()
                .addCriteria(Criteria.where("draftId").`is`(req.draftId))
                .addCriteria(Criteria.where("trackType").`is`(req.trackType))
                .addCriteria(Criteria.where("trackName").`is`(req.trackName))
            val exists = reactiveMongoTemplate.exists(query, TrackEntity::class.java).awaitSingle()
            if (exists) {
                logger.warn(
                    "同类轨道已存在同名轨道，draftId={}, trackType={}, trackName={}",
                    req.draftId,
                    req.trackType,
                    req.trackName
                )
                throw SysException.systemError("同类轨道中已存在名称为 '${req.trackName}' 的轨道，请更换名称")
            }
        }
        // 使用Mongo的ObjectId生成id
        val id = ObjectId().toHexString()
        val now = LocalDateTime.now()
        val track = TrackEntity(
            id = id,
            draftId = req.draftId,
            trackType = req.trackType,
            mute = req.mute,
            relativeIndex = req.relativeIndex,
            absoluteIndex = req.absoluteIndex,
            createTime = now,
            updateTime = now
        ).apply {
            if (!req.trackName.isNullOrBlank()) {
                trackName = req.trackName
            }
        }
        reactiveMongoTemplate.save(track).awaitSingle()
        logger.info("轨道添加成功，id={}", id)
        return id
    }
} 