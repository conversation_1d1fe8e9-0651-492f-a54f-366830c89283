# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/7 下午2:03
File Name:video.py
"""
from mcp.server.fastmcp import FastMCP
import pyJianYingDraft as draft
from config import DRAFT_FOLDER


def register_video_tools(mcp: FastMCP):
    @mcp.tool()
    def add_video_segment(draft_name: str, video_path: str, start_time: str = "0s", duration: str = "5s",
                          track_name: str = "video"):
        """
        向草稿添加视频片段

        Args:
            draft_name (str): 草稿名称
            video_path (str): 视频文件路径
            start_time (str, optional): 片段在轨道上的开始时间，默认为"0s"
            duration (str, optional): 片段持续时长，默认为"5s"
            track_name (str, optional): 轨道名称，默认为"video"

        Returns:
            str: 操作结果信息
        """
        try:
            # 创建草稿文件夹实例
            draft_folder = draft.DraftFolder(DRAFT_FOLDER)

            # 加载现有草稿
            script = draft_folder.load_template(draft_name)

            # 确保有视频轨道
            try:
                script.add_track(draft.TrackType.video, track_name)
            except:
                # 轨道可能已存在，忽略错误
                pass

            # 创建时间范围
            timerange = draft.trange(start_time, duration)

            # 创建视频片段（使用便捷构造，直接传入素材路径）
            video_segment = draft.VideoSegment(video_path, timerange)

            # 将片段添加到轨道中
            script.add_segment(video_segment, track_name)

            # 保存草稿
            script.save()

            return f"成功添加视频片段到草稿 '{draft_name}'，视频路径: {video_path}，时间范围: {start_time} - {duration}"

        except Exception as e:
            return f"添加视频片段失败: {str(e)}"

    @mcp.tool()
    def add_animation(draft_name: str, video_index: int, animation_type: str, animation_category: str = "intro", duration: str = None, track_name: str = "video"):
        """
        为草稿中的视频片段添加动画效果

        Args:
            draft_name (str): 草稿名称
            video_index (int): 视频片段在轨道中的索引（从0开始）
            animation_type (str): 动画类型名称，如"斜切"、"淡入"、"缩放"等
            animation_category (str, optional): 动画类别，可选值: "intro"(入场), "outro"(出场), "group"(组合)，默认为"intro"
            duration (str, optional): 动画持续时间，如"1s"、"0.5s"等，不指定则使用默认值
            track_name (str, optional): 轨道名称，默认为"video"

        Returns:
            str: 操作结果信息
        """
        try:
            # 创建草稿文件夹实例
            draft_folder = draft.DraftFolder(DRAFT_FOLDER)

            # 加载现有草稿
            script = draft_folder.load_template(draft_name)

            # 获取指定轨道
            if track_name not in script.tracks:
                return f"错误: 轨道 '{track_name}' 不存在"

            track = script.tracks[track_name]

            # 检查视频片段索引是否有效
            if video_index >= len(track.segments) or video_index < 0:
                return f"错误: 视频片段索引 {video_index} 超出范围，轨道中共有 {len(track.segments)} 个片段"

            # 获取视频片段
            video_segment = track.segments[video_index]

            # 检查是否为视频片段
            if not isinstance(video_segment, draft.VideoSegment):
                return f"错误: 索引 {video_index} 处的片段不是视频片段"

            # 根据动画类别选择对应的动画类型枚举
            animation_enum = None

            if animation_category.lower() == "intro":
                # 入场动画
                try:
                    animation_enum = getattr(draft.IntroType, animation_type)
                except AttributeError:
                    return f"错误: 未找到入场动画类型 '{animation_type}'"

            elif animation_category.lower() == "outro":
                # 出场动画
                try:
                    animation_enum = getattr(draft.OutroType, animation_type)
                except AttributeError:
                    return f"错误: 未找到出场动画类型 '{animation_type}'"

            elif animation_category.lower() == "group":
                # 组合动画
                try:
                    animation_enum = getattr(draft.GroupAnimationType, animation_type)
                except AttributeError:
                    return f"错误: 未找到组合动画类型 '{animation_type}'"

            else:
                return f"错误: 不支持的动画类别 '{animation_category}'，支持的类别: intro, outro, group"

            # 添加动画
            if duration:
                video_segment.add_animation(animation_enum, duration)
            else:
                video_segment.add_animation(animation_enum)

            # 保存草稿
            script.save()

            return f"成功为草稿 '{draft_name}' 中轨道 '{track_name}' 的第 {video_index} 个视频片段添加了 {animation_category} 动画 '{animation_type}'"

        except Exception as e:
            return f"添加动画失败: {str(e)}"

    @mcp.tool()
    def list_available_animations(animation_category: str = "intro"):
        """
        列出可用的动画类型

        Args:
            animation_category (str, optional): 动画类别，可选值: "intro"(入场), "outro"(出场), "group"(组合)，默认为"intro"

        Returns:
            str: 可用动画类型列表
        """
        try:
            animations = []

            if animation_category.lower() == "intro":
                # 获取所有入场动画
                for attr_name in dir(draft.IntroType):
                    if not attr_name.startswith('_') and hasattr(getattr(draft.IntroType, attr_name), 'value'):
                        animation_meta = getattr(draft.IntroType, attr_name).value
                        animations.append(f"{attr_name} - {animation_meta.name} (时长: {animation_meta.duration}s)")

            elif animation_category.lower() == "outro":
                # 获取所有出场动画
                for attr_name in dir(draft.OutroType):
                    if not attr_name.startswith('_') and hasattr(getattr(draft.OutroType, attr_name), 'value'):
                        animation_meta = getattr(draft.OutroType, attr_name).value
                        animations.append(f"{attr_name} - {animation_meta.name} (时长: {animation_meta.duration}s)")

            elif animation_category.lower() == "group":
                # 获取所有组合动画
                for attr_name in dir(draft.GroupAnimationType):
                    if not attr_name.startswith('_') and hasattr(getattr(draft.GroupAnimationType, attr_name), 'value'):
                        animation_meta = getattr(draft.GroupAnimationType, attr_name).value
                        animations.append(f"{attr_name} - {animation_meta.name} (时长: {animation_meta.duration}s)")

            else:
                return f"错误: 不支持的动画类别 '{animation_category}'，支持的类别: intro, outro, group"

            if not animations:
                return f"未找到 {animation_category} 类别的动画"

            result = f"可用的 {animation_category} 动画类型:\n"
            result += "\n".join(animations)
            return result

        except Exception as e:
            return f"获取动画列表失败: {str(e)}"

    @mcp.tool()
    def get_video_segments_info(draft_name: str, track_name: str = "video"):
        """
        获取草稿中指定轨道的视频片段信息

        Args:
            draft_name (str): 草稿名称
            track_name (str, optional): 轨道名称，默认为"video"

        Returns:
            str: 视频片段信息列表
        """
        try:
            # 创建草稿文件夹实例
            draft_folder = draft.DraftFolder(DRAFT_FOLDER)

            # 加载现有草稿
            script = draft_folder.load_template(draft_name)

            # 获取指定轨道
            if track_name not in script.tracks:
                return f"错误: 轨道 '{track_name}' 不存在"

            track = script.tracks[track_name]

            if not track.segments:
                return f"轨道 '{track_name}' 中没有片段"

            segments_info = []
            for i, segment in enumerate(track.segments):
                if isinstance(segment, draft.VideoSegment):
                    # 获取片段基本信息
                    start_time = segment.target_timerange.start / 1000000  # 转换为秒
                    duration = segment.target_timerange.duration / 1000000  # 转换为秒

                    # 获取动画信息
                    animations = []
                    if hasattr(segment, 'animations_instance') and segment.animations_instance:
                        for animation in segment.animations_instance.animations:
                            animations.append(f"{animation.animation_type}动画")

                    animation_info = f", 动画: {', '.join(animations)}" if animations else ", 无动画"

                    segments_info.append(f"索引 {i}: 开始时间 {start_time:.2f}s, 持续时长 {duration:.2f}s{animation_info}")
                else:
                    segments_info.append(f"索引 {i}: 非视频片段")

            result = f"草稿 '{draft_name}' 轨道 '{track_name}' 中的片段信息:\n"
            result += "\n".join(segments_info)
            return result

        except Exception as e:
            return f"获取视频片段信息失败: {str(e)}"
