# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/7 下午2:03
File Name:video.py
"""
from mcp.server.fastmcp import FastMCP
import pyJianYingDraft as draft
from config import DRAFT_FOLDER


def register_video_tools(mcp: FastMCP):
    @mcp.tool()
    def add_video_segment(draft_name: str, video_path: str, start_time: str = "0s", duration: str = "5s",
                          track_name: str = "video"):
        """
        向草稿添加视频片段

        Args:
            draft_name (str): 草稿名称
            video_path (str): 视频文件路径
            start_time (str, optional): 片段在轨道上的开始时间，默认为"0s"
            duration (str, optional): 片段持续时长，默认为"5s"
            track_name (str, optional): 轨道名称，默认为"video"

        Returns:
            str: 操作结果信息
        """
        try:
            # 创建草稿文件夹实例
            draft_folder = draft.DraftFolder(DRAFT_FOLDER)

            # 加载现有草稿
            script = draft_folder.load_template(draft_name)

            # 确保有视频轨道
            try:
                script.add_track(draft.TrackType.video, track_name)
            except:
                # 轨道可能已存在，忽略错误
                pass

            # 创建时间范围
            timerange = draft.trange(start_time, duration)

            # 创建视频片段（使用便捷构造，直接传入素材路径）
            video_segment = draft.VideoSegment(video_path, timerange)

            # 将片段添加到轨道中
            script.add_segment(video_segment, track_name)

            # 保存草稿
            script.save()

            return f"成功添加视频片段到草稿 '{draft_name}'，视频路径: {video_path}，时间范围: {start_time} - {duration}"

        except Exception as e:
            return f"添加视频片段失败: {str(e)}"
