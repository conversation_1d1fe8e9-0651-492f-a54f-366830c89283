# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON>i
<PERSON>reate Time: 2025/8/7 下午1:25
File Name:draft.py
"""
import uuid
from config import DRAFT_FOLDER
import pyJianYingDraft as draft

from mcp.server.fastmcp import FastMCP


def register_draft_tools(mcp: FastMCP):
    @mcp.tool()
    def create_draft(name: str = '', width: int = 1920, height: int = 1080, fps=30):
        """
        创建一个新的剪映草稿文件

        Args:
            name (str, optional): 草稿名称，默认为空字符串，会自动生成UUID作为名称
            width (int, optional): 草稿宽度，默认为1920
            height (int, optional): 草稿高度，默认为1080
            fps (int, optional): 草稿帧率，默认为30

        Returns:
            str: 草稿的JSON字符串表示
        """
        # 如果没有提供名称，则使用UUID生成唯一名称
        if not name:
            name = str(uuid.uuid4())

        # 创建草稿文件夹实例
        draft_folder = draft.DraftFolder(DRAFT_FOLDER)

        # 创建新的草稿
        script = draft_folder.create_draft(name, width, height, fps)
        script.save()
        return script.dumps()


