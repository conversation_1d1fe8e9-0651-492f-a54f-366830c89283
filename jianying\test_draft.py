# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON>i
<PERSON>reate Time: 2025/8/7
File Name: test_draft.py
Description: 剪映草稿功能简单测试和演示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from jianying.draft import create_draft
from jianying.track import add_track
from jianying.video_segment import add_video_segment, Timerange, ClipSettings
from jianying.config import DRAFT_FOLDER

# 测试视频文件路径
TEST_VIDEO_PATH = r"D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4"


def main():
    """三步骤演示：创建草稿、添加视频轨道、添加视频"""
    print("🎬 剪映草稿功能三步骤演示")
    print("=" * 50)

    # 检查测试视频文件是否存在
    if not os.path.exists(TEST_VIDEO_PATH):
        print(f"⚠️  警告: 测试视频文件不存在: {TEST_VIDEO_PATH}")
        print("将继续进行测试，但视频相关功能可能会失败")

    # 步骤1: 创建草稿
    print("\n📝 步骤1: 创建草稿...")
    import time
    timestamp = int(time.time())
    draft_result = create_draft(
        name=f"三步骤演示项目_{timestamp}",
        width=1920,
        height=1080,
        draft_path=DRAFT_FOLDER
    )
    print(f"   ✅ 草稿创建成功: {draft_result['draft_name']}")
    print(f"   📋 草稿ID: {draft_result['draft_id']}")

    draft_id = draft_result['draft_id']

    # 步骤2: 添加视频轨道
    print("\n🎞️  步骤2: 添加视频轨道...")
    video_track = add_track(
        draft_id=draft_id,
        track_type="video",
        track_name="主视频轨道",
        draft_path=DRAFT_FOLDER
    )
    print(f"   ✅ 视频轨道添加成功: {video_track['track_name']}")
    print(f"   🆔 轨道ID: {video_track['track_id']}")

    video_track_id = video_track['track_id']

    # 步骤3: 添加视频
    print(f"\n🎥 步骤3: 添加视频...")
    video_segment = add_video_segment(
        draft_id=draft_id,
        resource_path=TEST_VIDEO_PATH,
        target_timerange=Timerange(start=0, duration=5000),  # 5秒（匹配测试视频的实际长度）
        track_id=video_track_id,
        draft_path=DRAFT_FOLDER
    )
    print(f"   ✅ 视频添加成功: {video_segment['segment_id']}")
    print(f"   📹 视频路径: {TEST_VIDEO_PATH}")

    print(f"\n🎉 三步骤演示完成!")
    print(f"📁 存储路径: {DRAFT_FOLDER}")
    print(f"📋 最终结果:")
    print(f"   - 草稿: {draft_result['draft_name']} ({draft_id})")
    print(f"   - 视频轨道: {video_track['track_name']} ({video_track_id})")
    print(f"   - 视频片段: {video_segment['segment_id']} (10秒)")


if __name__ == "__main__":
    main()
